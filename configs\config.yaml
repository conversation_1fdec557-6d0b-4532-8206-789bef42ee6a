app:
  name: digital-transformation-api
  port: 8080
  env: dev
  timeZone: UTC

log:
  level: info
  maskingList: 

db:
  main:
    driver: pg
    dsn: postgres://postgres:test12345*@localhost:5432/main?sslmode=disable
    maxIdleConns: 5
    maxConns: 10
    maxLifeTime: 1h

memstore:
  main:
    host: localhost
    port: 6379
    password: 
    db: 0
    insecureSkipVerify:

integrations:
  example:
    post:
      baseUrl: http://localhost:8080
      timeout: 10s
      method: POST
      url: /v1
