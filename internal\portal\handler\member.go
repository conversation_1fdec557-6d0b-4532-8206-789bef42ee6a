package handler

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/portal/port/call-api"
	calldb "digital-transformation-api/internal/portal/port/call-db"
	"digital-transformation-api/internal/portal/service/member"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type memberHandler struct {
	service member.Service
}

func NewMemberHandler(service member.Service) *memberHandler {
	return &memberHandler{
		service: service,
	}
}

func (h *memberHandler) Handle(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.Request
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	response, err := h.service.Execute(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

func BindMemberRoute(app gins.GinApps) {
	svc := member.New(
		calldb.NewAdaptorPG(infrastructure.Db),
		callapi.NewAdaptorAPI(callapi.NewClient()),
	)

	hdl := NewMemberHandler(svc)
	app.Register(
		http.MethodGet,
		"/member",
		app.ParseRouteContext(hdl.Handle),
	)
}
