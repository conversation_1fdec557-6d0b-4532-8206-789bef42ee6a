package gins

import (
	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func getID<PERSON>y<PERSON><PERSON>(key string, ctx *gin.Context) string {
	id := ctx.GetHeader(key)
	if id == "" {
		id = uuid.NewString()
		ctx.Request.Header.Set(key, id)
	}

	return id
}

func NewCoreLogFromCtx(ctx *gin.Context) logger.CoreLogger {
	return logs.NewCoreLog(map[string]any{
		apps.TraceID: getID<PERSON><PERSON><PERSON><PERSON>(apps.TraceID, ctx),
		apps.SpanID:  getID<PERSON>y<PERSON><PERSON>(apps.SpanID, ctx),
	})
}

func NewLogFromCtx(ctx *gin.Context) logger.Logger {
	return logs.NewCoreLog(map[string]any{
		apps.TraceID: getID<PERSON>y<PERSON><PERSON>(apps.TraceID, ctx),
		apps.SpanID:  getID<PERSON><PERSON><PERSON><PERSON>(apps.SpanID, ctx),
	})
}
